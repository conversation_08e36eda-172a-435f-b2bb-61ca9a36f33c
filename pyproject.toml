[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "lib2docscrape"
version = "0.1.0"
description = "A comprehensive web scraping tool for library documentation with multiple backend support"
authors = [{name = "lib2docScrape Team"}]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["documentation", "scraping", "web-scraping", "library", "docs"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Documentation",
    "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
]
dependencies = [
    # Core web scraping and HTTP
    "aiohttp>=3.9.1",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.2",
    "scrapy>=2.11.0",
    # Browser automation
    "playwright>=1.40.0",
    # Async file operations
    "aiofiles>=23.0.0",
    # Data processing and validation
    "pydantic>=2.5.2",
    "markdownify>=0.11.6",
    "bleach>=6.0.0",
    # Format processing
    "markdown>=3.4.0",
    "docutils>=0.18.0",
    "networkx>=3.0.0",
    # URL and domain handling
    "tldextract>=3.1.0",
    "certifi>=2024.2.2",
    # Search functionality
    "duckduckgo-search>=4.4.1",
    # Data analysis and visualization (needed for benchmarking)
    "pandas>=2.0.0",
    "matplotlib>=3.7.0",
    # File system monitoring
    "watchdog>=3.0.0",
    # HTTP mocking for tests
    "responses>=0.25.0",
    "aioresponses>=0.7.0",
    # Testing framework
    "pytest>=8.3.4",
    "pytest-asyncio>=0.25.3",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.14.0",
    "pytest-xdist>=3.6.0",
    "pre-commit>=4.2.0",
]

[project.optional-dependencies]
dev = [
    # Testing framework
    "pytest>=8.3.4",
    "pytest-cov>=6.0.0",
    "pytest-asyncio>=0.24.0",
    "pytest-mock>=3.10.0",
    "pytest-xdist>=3.6.0",
    "hypothesis>=6.92.1",

    # HTTP mocking for tests
    "responses>=0.25.0",
    "aioresponses>=0.7.0",

    # Code quality and coverage
    "coverage[toml]>=7.6.10",
    "ruff>=0.11.11",

    # Performance and async
    "uvloop>=0.19.0",

    # Web framework for GUI/API
    "fastapi>=0.110.0",
    "httpx>=0.27.0",
    "jinja2>=3.1.2",

    # Data analysis and visualization
    "pandas>=2.0.0",
    "matplotlib>=3.7.0",

    # File system monitoring
    "watchdog>=3.0.0",

    # NLP and ML (optional features)
    "scikit-learn>=1.3.0",
    "nltk>=3.8.1",

    # System monitoring
    "psutil>=5.9.0",

    # Compression utilities
    "bz2file>=0.98",
]

# Backend-specific optional dependencies
crawl4ai = [
    "crawl4ai>=0.2.0",
]

playwright = [
    "playwright>=1.40.0",
]

lightpanda = [
    # Lightpanda backend dependencies would go here
]

all = [
    "lib2docscrape[dev,crawl4ai,playwright,lightpanda]",
]

[tool.pytest.ini_options]
pythonpath = ["."]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
# For parallel execution, use: pytest -n auto or pytest -n 2
# Parallel execution is beneficial for larger test suites or slower tests
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "http_mocking: marks tests that use HTTP mocking",
    "scrapy: marks tests that use Scrapy backend",
    "real_world: marks tests that use real websites",
    "performance: marks tests as performance benchmarks",
    "e2e: marks tests as end-to-end tests",
]

[tool.ruff]
target-version = "py39"
line-length = 88
exclude = [
    # Version control
    ".git",
    ".hg",
    ".svn",

    # Build and distribution
    "build",
    "dist",
    "*.egg-info",

    # Virtual environments
    ".venv",
    "venv",
    ".env",

    # Cache directories
    ".pytest_cache",
    ".ruff_cache",
    ".mypy_cache",
    "__pycache__",

    # Documentation and reports
    "docs",
    "cline_docs",
    "htmlcov",
    "archive",

    # Static files and templates
    "static",
    "templates",
    "custom_static",
    "custom_templates",
    "test_static",
    "test_templates",

    # Other
    "version_history",
    "node_modules",
]

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
]
ignore = [
    "E501",  # line too long (handled by formatter)
    "B008",  # do not perform function calls in argument defaults
]
[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]  # Allow unused imports in __init__.py
"tests/*" = ["B011"]      # Allow assert False in tests

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"
# Like Black, indent with spaces, rather than tabs.
indent-style = "space"
# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false
# Like Black, automatically detect the appropriate line length.
line-ending = "auto"

[tool.coverage.run]
branch = true
parallel = true
source = ["src"]
omit = [
    "*/tests/*",
    "tests/*",
    "*/__init__.py",
    "*/site-packages/*",
    "*/venv/*",
    ".venv/*",
    "*/.venv/*",
    "*/.uv/*",
    "setup.py",
    "run.py",
    "debug_*.py",
]

[tool.coverage.report]
fail_under = 20  # Current realistic target based on existing coverage
show_missing = true
skip_covered = true
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "@(abc\\.)?abstractmethod",
    "@(typing\\.)?overload",
]

[tool.coverage.html]
directory = "htmlcov"

[dependency-groups]
dev = [
    "aioresponses>=0.7.8",
    "pandas>=2.2.3",
]
